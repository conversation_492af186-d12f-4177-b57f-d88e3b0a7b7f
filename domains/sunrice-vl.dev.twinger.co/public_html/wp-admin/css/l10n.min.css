/*! This file is auto-generated */
body.rtl,body.rtl .press-this a.wp-switch-editor{font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON>,sans-serif}.rtl h1,.rtl h2,.rtl h3,.rtl h4,.rtl h5,.rtl h6{font-family:Arial,sans-serif;font-weight:600}body.locale-he-il,body.locale-he-il .press-this a.wp-switch-editor{font-family:Arial,sans-serif}.locale-he-il em{font-style:normal;font-weight:600}.locale-zh-cn #local-time,.locale-zh-cn #utc-time,.locale-zh-cn .form-wrap p,.locale-zh-cn .howto,.locale-zh-cn .inline-edit-row fieldset span.checkbox-title,.locale-zh-cn .inline-edit-row fieldset span.title,.locale-zh-cn .js .input-with-default-title,.locale-zh-cn .link-to-original,.locale-zh-cn .tablenav .displaying-num,.locale-zh-cn p.description,.locale-zh-cn p.help,.locale-zh-cn p.install-help,.locale-zh-cn span.description{font-style:normal}.locale-zh-cn .hdnle a{font-size:12px}.locale-zh-cn form.upgrade .hint{font-style:normal;font-size:100%}.locale-zh-cn #sort-buttons{font-size:1em!important}.locale-de-de #customize-header-actions .button,.locale-de-de-formal #customize-header-actions .button{padding:0 5px 1px}.locale-de-de #customize-header-actions .spinner,.locale-de-de-formal #customize-header-actions .spinner{margin:16px 3px 0}body[class*=locale-de-] .inline-edit-row fieldset label span.title,body[class*=locale-de-] .inline-edit-row fieldset.inline-edit-date legend{width:7em}body[class*=locale-de-] .inline-edit-row fieldset .timestamp-wrap,body[class*=locale-de-] .inline-edit-row fieldset label span.input-text-wrap{margin-left:7em}.locale-ru-ru #adminmenu{width:inherit}.locale-ru-ru #adminmenu,.locale-ru-ru #wpbody{margin-left:0}.locale-ru-ru .inline-edit-row fieldset label span.title,.locale-ru-ru .inline-edit-row fieldset.inline-edit-date legend{width:8em}.locale-ru-ru .inline-edit-row fieldset .timestamp-wrap,.locale-ru-ru .inline-edit-row fieldset label span.input-text-wrap{margin-left:8em}.locale-ru-ru.post-new-php .tagsdiv .newtag,.locale-ru-ru.post-php .tagsdiv .newtag{width:165px}.locale-ru-ru.press-this .posting{margin-right:277px}.locale-ru-ru .press-this-sidebar{width:265px}.locale-ru-ru #customize-header-actions .button{padding:0 5px 1px}.locale-ru-ru #customize-header-actions .spinner{margin:16px 3px 0}.locale-lt-lt .inline-edit-row fieldset label span.title,.locale-lt-lt .inline-edit-row fieldset.inline-edit-date legend{width:8em}.locale-lt-lt .inline-edit-row fieldset .timestamp-wrap,.locale-lt-lt .inline-edit-row fieldset label span.input-text-wrap{margin-left:8em}.locale-lt-lt .quick-edit-row-post fieldset.inline-edit-col-right label span.title,.locale-ru-ru .quick-edit-row-post fieldset.inline-edit-col-right label span.title,body[class*=locale-de-] .quick-edit-row-post fieldset.inline-edit-col-right label span.title{width:auto}@media screen and (max-width:782px){.locale-lt-lt .inline-edit-row fieldset .timestamp-wrap,.locale-lt-lt .inline-edit-row fieldset label span.input-text-wrap,.locale-ru-ru .inline-edit-row fieldset .timestamp-wrap,.locale-ru-ru .inline-edit-row fieldset label span.input-text-wrap,body[class*=locale-de-] .inline-edit-row fieldset .timestamp-wrap,body[class*=locale-de-] .inline-edit-row fieldset label span.input-text-wrap{margin-left:0}}