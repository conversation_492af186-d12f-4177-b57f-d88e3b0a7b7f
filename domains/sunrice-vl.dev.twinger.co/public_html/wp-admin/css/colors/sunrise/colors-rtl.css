/*! This file is auto-generated */
/*
 * Button mixin- creates a button effect with correct
 * highlights/shadows, based on a base color.
 */
/**
 * This function name uses British English to maintain backward compatibility, as developers
 * may use the function in their own admin CSS files. See #56811.
 */
body {
  background: #f1f1f1;
}

/* Links */
a {
  color: #0073aa;
}
a:hover, a:active, a:focus {
  color: rgb(0, 149.5, 221);
}

#post-body .misc-pub-post-status:before,
#post-body #visibility:before,
.curtime #timestamp:before,
#post-body .misc-pub-revisions:before,
span.wp-media-buttons-icon:before {
  color: currentColor;
}

.wp-core-ui .button-link {
  color: #0073aa;
}
.wp-core-ui .button-link:hover, .wp-core-ui .button-link:active, .wp-core-ui .button-link:focus {
  color: rgb(0, 149.5, 221);
}

.media-modal .delete-attachment,
.media-modal .trash-attachment,
.media-modal .untrash-attachment,
.wp-core-ui .button-link-delete {
  color: #a00;
}

.media-modal .delete-attachment:hover,
.media-modal .trash-attachment:hover,
.media-modal .untrash-attachment:hover,
.media-modal .delete-attachment:focus,
.media-modal .trash-attachment:focus,
.media-modal .untrash-attachment:focus,
.wp-core-ui .button-link-delete:hover,
.wp-core-ui .button-link-delete:focus {
  color: #dc3232;
}

/* Forms */
input[type=checkbox]:checked::before {
  content: url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M14.83%204.89l1.34.94-5.81%208.38H9.02L5.78%209.67l1.34-1.25%202.57%202.4z%27%20fill%3D%27%237e8993%27%2F%3E%3C%2Fsvg%3E");
}

input[type=radio]:checked::before {
  background: #7e8993;
}

.wp-core-ui input[type=reset]:hover,
.wp-core-ui input[type=reset]:active {
  color: rgb(0, 149.5, 221);
}

input[type=text]:focus,
input[type=password]:focus,
input[type=color]:focus,
input[type=date]:focus,
input[type=datetime]:focus,
input[type=datetime-local]:focus,
input[type=email]:focus,
input[type=month]:focus,
input[type=number]:focus,
input[type=search]:focus,
input[type=tel]:focus,
input[type=text]:focus,
input[type=time]:focus,
input[type=url]:focus,
input[type=week]:focus,
input[type=checkbox]:focus,
input[type=radio]:focus,
select:focus,
textarea:focus {
  border-color: #dd823b;
  box-shadow: 0 0 0 1px #dd823b;
}

/* Core UI */
.wp-core-ui .button {
  border-color: #7e8993;
  color: #32373c;
}
.wp-core-ui .button.hover,
.wp-core-ui .button:hover,
.wp-core-ui .button.focus,
.wp-core-ui .button:focus {
  border-color: rgb(112.7848101266, 124.2721518987, 134.7151898734);
  color: rgb(38.4090909091, 42.25, 46.0909090909);
}
.wp-core-ui .button.focus,
.wp-core-ui .button:focus {
  border-color: #7e8993;
  color: rgb(38.4090909091, 42.25, 46.0909090909);
  box-shadow: 0 0 0 1px #32373c;
}
.wp-core-ui .button:active {
  border-color: #7e8993;
  color: rgb(38.4090909091, 42.25, 46.0909090909);
  box-shadow: none;
}
.wp-core-ui .button.active,
.wp-core-ui .button.active:focus,
.wp-core-ui .button.active:hover {
  border-color: #dd823b;
  color: rgb(38.4090909091, 42.25, 46.0909090909);
  box-shadow: inset 0 2px 5px -3px #dd823b;
}
.wp-core-ui .button.active:focus {
  box-shadow: 0 0 0 1px #32373c;
}
.wp-core-ui .button,
.wp-core-ui .button-secondary {
  color: #dd823b;
  border-color: #dd823b;
}
.wp-core-ui .button.hover,
.wp-core-ui .button:hover,
.wp-core-ui .button-secondary:hover {
  border-color: rgb(195.147826087, 104.5434782609, 33.852173913);
  color: rgb(195.147826087, 104.5434782609, 33.852173913);
}
.wp-core-ui .button.focus,
.wp-core-ui .button:focus,
.wp-core-ui .button-secondary:focus {
  border-color: rgb(228.5391304348, 157.7173913043, 102.4608695652);
  color: rgb(151.6869565217, 81.2608695652, 26.3130434783);
  box-shadow: 0 0 0 1px rgb(228.5391304348, 157.7173913043, 102.4608695652);
}
.wp-core-ui .button-primary:hover {
  color: #fff;
}
.wp-core-ui .button-primary {
  background: #dd823b;
  border-color: #dd823b;
  color: #fff;
}
.wp-core-ui .button-primary:hover, .wp-core-ui .button-primary:focus {
  background: rgb(223.2617391304, 138.3152173913, 72.0382608696);
  border-color: rgb(218.7382608696, 121.6847826087, 45.9617391304);
  color: #fff;
}
.wp-core-ui .button-primary:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #dd823b;
}
.wp-core-ui .button-primary:active {
  background: rgb(216.**********, 116.**********, 37.**********);
  border-color: rgb(216.**********, 116.**********, 37.**********);
  color: #fff;
}
.wp-core-ui .button-primary.active, .wp-core-ui .button-primary.active:focus, .wp-core-ui .button-primary.active:hover {
  background: #dd823b;
  color: #fff;
  border-color: rgb(173.4173913043, 92.902173913, 30.0826086957);
  box-shadow: inset 0 2px 5px -3px rgb(21.3043478261, 11.4130434783, 3.6956521739);
}
.wp-core-ui .button-group > .button.active {
  border-color: #dd823b;
}
.wp-core-ui .wp-ui-primary {
  color: #fff;
  background-color: #cf4944;
}
.wp-core-ui .wp-ui-text-primary {
  color: #cf4944;
}
.wp-core-ui .wp-ui-highlight {
  color: #fff;
  background-color: #dd823b;
}
.wp-core-ui .wp-ui-text-highlight {
  color: #dd823b;
}
.wp-core-ui .wp-ui-notification {
  color: #fff;
  background-color: #ccaf0b;
}
.wp-core-ui .wp-ui-text-notification {
  color: #ccaf0b;
}
.wp-core-ui .wp-ui-text-icon {
  color: hsl(2.1582733813, 7%, 95%);
}

/* List tables */
.wrap .page-title-action,
.wrap .page-title-action:active {
  border: 1px solid #dd823b;
  color: #dd823b;
}

.wrap .page-title-action:hover {
  color: rgb(195.147826087, 104.5434782609, 33.852173913);
  border-color: rgb(195.147826087, 104.5434782609, 33.852173913);
}

.wrap .page-title-action:focus {
  border-color: rgb(228.5391304348, 157.7173913043, 102.4608695652);
  color: rgb(151.6869565217, 81.2608695652, 26.3130434783);
  box-shadow: 0 0 0 1px rgb(228.5391304348, 157.7173913043, 102.4608695652);
}

.view-switch a.current:before {
  color: #cf4944;
}

.view-switch a:hover:before {
  color: #ccaf0b;
}

/* Admin Menu */
#adminmenuback,
#adminmenuwrap,
#adminmenu {
  background: #cf4944;
}

#adminmenu a {
  color: #fff;
}

#adminmenu div.wp-menu-image:before {
  color: hsl(2.1582733813, 7%, 95%);
}

#adminmenu a:hover,
#adminmenu li.menu-top:hover,
#adminmenu li.opensub > a.menu-top,
#adminmenu li > a.menu-top:focus {
  color: #fff;
  background-color: #dd823b;
}

#adminmenu li.menu-top:hover div.wp-menu-image:before,
#adminmenu li.opensub > a.menu-top div.wp-menu-image:before {
  color: #fff;
}

/* Active tabs use a bottom border color that matches the page background color. */
.about-wrap .nav-tab-active,
.nav-tab-active,
.nav-tab-active:hover {
  background-color: #f1f1f1;
  border-bottom-color: #f1f1f1;
}

/* Admin Menu: submenu */
#adminmenu .wp-submenu,
#adminmenu .wp-has-current-submenu .wp-submenu,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu {
  background: rgb(190.4217021277, 53.969787234, 48.8782978723);
}

#adminmenu li.wp-has-submenu.wp-not-current-submenu.opensub:hover:after,
#adminmenu li.wp-has-submenu.wp-not-current-submenu:focus-within:after {
  border-left-color: rgb(190.4217021277, 53.969787234, 48.8782978723);
}

#adminmenu .wp-submenu .wp-submenu-head {
  color: rgb(240.6, 200.4, 198.9);
}

#adminmenu .wp-submenu a,
#adminmenu .wp-has-current-submenu .wp-submenu a,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu a,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a {
  color: rgb(240.6, 200.4, 198.9);
}
#adminmenu .wp-submenu a:focus, #adminmenu .wp-submenu a:hover,
#adminmenu .wp-has-current-submenu .wp-submenu a:focus,
#adminmenu .wp-has-current-submenu .wp-submenu a:hover,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu a:focus,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu a:hover,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a:focus,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a:hover {
  color: rgb(247.**********, 227.**********, 211.**********);
}

/* Admin Menu: current */
#adminmenu .wp-submenu li.current a,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu li.current a,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a {
  color: #fff;
}
#adminmenu .wp-submenu li.current a:hover, #adminmenu .wp-submenu li.current a:focus,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu li.current a:hover,
#adminmenu a.wp-has-current-submenu:focus + .wp-submenu li.current a:focus,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a:hover,
#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a:focus {
  color: rgb(247.**********, 227.**********, 211.**********);
}

ul#adminmenu a.wp-has-current-submenu:after,
ul#adminmenu > li.current > a.current:after {
  border-left-color: #f1f1f1;
}

#adminmenu li.current a.menu-top,
#adminmenu li.wp-has-current-submenu a.wp-has-current-submenu,
#adminmenu li.wp-has-current-submenu .wp-submenu .wp-submenu-head,
.folded #adminmenu li.current.menu-top {
  color: #fff;
  background: #dd823b;
}

#adminmenu li.wp-has-current-submenu div.wp-menu-image:before,
#adminmenu a.current:hover div.wp-menu-image:before,
#adminmenu li.current div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu a:focus div.wp-menu-image:before,
#adminmenu li.wp-has-current-submenu.opensub div.wp-menu-image:before,
#adminmenu li:hover div.wp-menu-image:before,
#adminmenu li a:focus div.wp-menu-image:before,
#adminmenu li.opensub div.wp-menu-image:before {
  color: #fff;
}

/* Admin Menu: bubble */
#adminmenu .menu-counter,
#adminmenu .awaiting-mod,
#adminmenu .update-plugins {
  color: #fff;
  background: #ccaf0b;
}

#adminmenu li.current a .awaiting-mod,
#adminmenu li a.wp-has-current-submenu .update-plugins,
#adminmenu li:hover a .awaiting-mod,
#adminmenu li.menu-top:hover > a .update-plugins {
  color: #fff;
  background: rgb(190.4217021277, 53.969787234, 48.8782978723);
}

/* Admin Menu: collapse button */
#collapse-button {
  color: hsl(2.1582733813, 7%, 95%);
}

#collapse-button:hover,
#collapse-button:focus {
  color: rgb(247.**********, 227.**********, 211.**********);
}

/* Admin Bar */
#wpadminbar {
  color: #fff;
  background: #cf4944;
}

#wpadminbar .ab-item,
#wpadminbar a.ab-item,
#wpadminbar > #wp-toolbar span.ab-label,
#wpadminbar > #wp-toolbar span.noticon {
  color: #fff;
}

#wpadminbar .ab-icon,
#wpadminbar .ab-icon:before,
#wpadminbar .ab-item:before,
#wpadminbar .ab-item:after {
  color: hsl(2.1582733813, 7%, 95%);
}

#wpadminbar:not(.mobile) .ab-top-menu > li:hover > .ab-item,
#wpadminbar:not(.mobile) .ab-top-menu > li > .ab-item:focus,
#wpadminbar.nojq .quicklinks .ab-top-menu > li > .ab-item:focus,
#wpadminbar.nojs .ab-top-menu > li.menupop:hover > .ab-item,
#wpadminbar .ab-top-menu > li.menupop.hover > .ab-item {
  color: rgb(247.**********, 227.**********, 211.**********);
  background: rgb(190.4217021277, 53.969787234, 48.8782978723);
}

#wpadminbar:not(.mobile) > #wp-toolbar li:hover span.ab-label,
#wpadminbar:not(.mobile) > #wp-toolbar li.hover span.ab-label,
#wpadminbar:not(.mobile) > #wp-toolbar a:focus span.ab-label {
  color: rgb(247.**********, 227.**********, 211.**********);
}

#wpadminbar:not(.mobile) li:hover .ab-icon:before,
#wpadminbar:not(.mobile) li:hover .ab-item:before,
#wpadminbar:not(.mobile) li:hover .ab-item:after,
#wpadminbar:not(.mobile) li:hover #adminbarsearch:before {
  color: rgb(247.**********, 227.**********, 211.**********);
}

/* Admin Bar: submenu */
#wpadminbar .menupop .ab-sub-wrapper {
  background: rgb(190.4217021277, 53.969787234, 48.8782978723);
}

#wpadminbar .quicklinks .menupop ul.ab-sub-secondary,
#wpadminbar .quicklinks .menupop ul.ab-sub-secondary .ab-submenu {
  background: rgb(207.3164148936, 107.1221761059, 103.3835851064);
}

#wpadminbar .ab-submenu .ab-item,
#wpadminbar .quicklinks .menupop ul li a,
#wpadminbar .quicklinks .menupop.hover ul li a,
#wpadminbar.nojs .quicklinks .menupop:hover ul li a {
  color: rgb(240.6, 200.4, 198.9);
}

#wpadminbar .quicklinks li .blavatar,
#wpadminbar .menupop .menupop > .ab-item:before {
  color: hsl(2.1582733813, 7%, 95%);
}

#wpadminbar .quicklinks .menupop ul li a:hover,
#wpadminbar .quicklinks .menupop ul li a:focus,
#wpadminbar .quicklinks .menupop ul li a:hover strong,
#wpadminbar .quicklinks .menupop ul li a:focus strong,
#wpadminbar .quicklinks .ab-sub-wrapper .menupop.hover > a,
#wpadminbar .quicklinks .menupop.hover ul li a:hover,
#wpadminbar .quicklinks .menupop.hover ul li a:focus,
#wpadminbar.nojs .quicklinks .menupop:hover ul li a:hover,
#wpadminbar.nojs .quicklinks .menupop:hover ul li a:focus,
#wpadminbar li:hover .ab-icon:before,
#wpadminbar li:hover .ab-item:before,
#wpadminbar li a:focus .ab-icon:before,
#wpadminbar li .ab-item:focus:before,
#wpadminbar li .ab-item:focus .ab-icon:before,
#wpadminbar li.hover .ab-icon:before,
#wpadminbar li.hover .ab-item:before,
#wpadminbar li:hover #adminbarsearch:before,
#wpadminbar li #adminbarsearch.adminbar-focused:before {
  color: rgb(247.**********, 227.**********, 211.**********);
}

#wpadminbar .quicklinks li a:hover .blavatar,
#wpadminbar .quicklinks li a:focus .blavatar,
#wpadminbar .quicklinks .ab-sub-wrapper .menupop.hover > a .blavatar,
#wpadminbar .menupop .menupop > .ab-item:hover:before,
#wpadminbar.mobile .quicklinks .ab-icon:before,
#wpadminbar.mobile .quicklinks .ab-item:before {
  color: rgb(247.**********, 227.**********, 211.**********);
}

#wpadminbar.mobile .quicklinks .hover .ab-icon:before,
#wpadminbar.mobile .quicklinks .hover .ab-item:before {
  color: hsl(2.1582733813, 7%, 95%);
}

/* Admin Bar: search */
#wpadminbar #adminbarsearch:before {
  color: hsl(2.1582733813, 7%, 95%);
}

#wpadminbar > #wp-toolbar > #wp-admin-bar-top-secondary > #wp-admin-bar-search #adminbarsearch input.adminbar-input:focus {
  color: #fff;
  background: rgb(214.**********, 100.**********, 96.**********);
}

/* Admin Bar: recovery mode */
#wpadminbar #wp-admin-bar-recovery-mode {
  color: #fff;
  background-color: #ccaf0b;
}

#wpadminbar #wp-admin-bar-recovery-mode .ab-item,
#wpadminbar #wp-admin-bar-recovery-mode a.ab-item {
  color: #fff;
}

#wpadminbar .ab-top-menu > #wp-admin-bar-recovery-mode.hover > .ab-item,
#wpadminbar.nojq .quicklinks .ab-top-menu > #wp-admin-bar-recovery-mode > .ab-item:focus,
#wpadminbar:not(.mobile) .ab-top-menu > #wp-admin-bar-recovery-mode:hover > .ab-item,
#wpadminbar:not(.mobile) .ab-top-menu > #wp-admin-bar-recovery-mode > .ab-item:focus {
  color: #fff;
  background-color: rgb(183.6, 157.5, 9.9);
}

/* Admin Bar: my account */
#wpadminbar .quicklinks li#wp-admin-bar-my-account.with-avatar > a img {
  border-color: rgb(214.**********, 100.**********, 96.**********);
  background-color: rgb(214.**********, 100.**********, 96.**********);
}

#wpadminbar #wp-admin-bar-user-info .display-name {
  color: #fff;
}

#wpadminbar #wp-admin-bar-user-info a:hover .display-name {
  color: rgb(247.**********, 227.**********, 211.**********);
}

#wpadminbar #wp-admin-bar-user-info .username {
  color: rgb(240.6, 200.4, 198.9);
}

/* Pointers */
.wp-pointer .wp-pointer-content h3 {
  background-color: #dd823b;
  border-color: rgb(216.**********, 116.**********, 37.**********);
}

.wp-pointer .wp-pointer-content h3:before {
  color: #dd823b;
}

.wp-pointer.wp-pointer-top .wp-pointer-arrow,
.wp-pointer.wp-pointer-top .wp-pointer-arrow-inner,
.wp-pointer.wp-pointer-undefined .wp-pointer-arrow,
.wp-pointer.wp-pointer-undefined .wp-pointer-arrow-inner {
  border-bottom-color: #dd823b;
}

/* Media */
.media-item .bar,
.media-progress-bar div {
  background-color: #dd823b;
}

.details.attachment {
  box-shadow: inset 0 0 0 3px #fff, inset 0 0 0 7px #dd823b;
}

.attachment.details .check {
  background-color: #dd823b;
  box-shadow: 0 0 0 1px #fff, 0 0 0 2px #dd823b;
}

.media-selection .attachment.selection.details .thumbnail {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #dd823b;
}

/* Themes */
.theme-browser .theme.active .theme-name,
.theme-browser .theme.add-new-theme a:hover:after,
.theme-browser .theme.add-new-theme a:focus:after {
  background: #dd823b;
}

.theme-browser .theme.add-new-theme a:hover span:after,
.theme-browser .theme.add-new-theme a:focus span:after {
  color: #dd823b;
}

.theme-section.current,
.theme-filter.current {
  border-bottom-color: #cf4944;
}

body.more-filters-opened .more-filters {
  color: #fff;
  background-color: #cf4944;
}

body.more-filters-opened .more-filters:before {
  color: #fff;
}

body.more-filters-opened .more-filters:hover,
body.more-filters-opened .more-filters:focus {
  background-color: #dd823b;
  color: #fff;
}

body.more-filters-opened .more-filters:hover:before,
body.more-filters-opened .more-filters:focus:before {
  color: #fff;
}

/* Widgets */
.widgets-chooser li.widgets-chooser-selected {
  background-color: #dd823b;
  color: #fff;
}

.widgets-chooser li.widgets-chooser-selected:before,
.widgets-chooser li.widgets-chooser-selected:focus:before {
  color: #fff;
}

/* Nav Menus */
.nav-menus-php .item-edit:focus:before {
  box-shadow: 0 0 0 1px rgb(228.5391304348, 157.7173913043, 102.4608695652), 0 0 2px 1px #dd823b;
}

/* Responsive Component */
div#wp-responsive-toggle a:before {
  color: hsl(2.1582733813, 7%, 95%);
}

.wp-responsive-open div#wp-responsive-toggle a {
  border-color: transparent;
  background: #dd823b;
}

.wp-responsive-open #wpadminbar #wp-admin-bar-menu-toggle a {
  background: rgb(190.4217021277, 53.969787234, 48.8782978723);
}

.wp-responsive-open #wpadminbar #wp-admin-bar-menu-toggle .ab-icon:before {
  color: hsl(2.1582733813, 7%, 95%);
}

/* TinyMCE */
.mce-container.mce-menu .mce-menu-item:hover,
.mce-container.mce-menu .mce-menu-item.mce-selected,
.mce-container.mce-menu .mce-menu-item:focus,
.mce-container.mce-menu .mce-menu-item-normal.mce-active,
.mce-container.mce-menu .mce-menu-item-preview.mce-active {
  background: #dd823b;
}

/* Customizer */
.wp-core-ui #customize-controls .control-section:hover > .accordion-section-title,
.wp-core-ui #customize-controls .control-section .accordion-section-title:hover,
.wp-core-ui #customize-controls .control-section.open .accordion-section-title,
.wp-core-ui #customize-controls .control-section .accordion-section-title:focus {
  color: #0073aa;
  border-right-color: #dd823b;
}
.wp-core-ui .customize-controls-close:focus,
.wp-core-ui .customize-controls-close:hover,
.wp-core-ui .customize-controls-preview-toggle:focus,
.wp-core-ui .customize-controls-preview-toggle:hover {
  color: #0073aa;
  border-top-color: #dd823b;
}
.wp-core-ui .customize-panel-back:hover,
.wp-core-ui .customize-panel-back:focus,
.wp-core-ui .customize-section-back:hover,
.wp-core-ui .customize-section-back:focus {
  color: #0073aa;
  border-right-color: #dd823b;
}
.wp-core-ui .customize-screen-options-toggle:hover,
.wp-core-ui .customize-screen-options-toggle:active,
.wp-core-ui .customize-screen-options-toggle:focus,
.wp-core-ui .active-menu-screen-options .customize-screen-options-toggle,
.wp-core-ui #customize-controls .customize-info.open.active-menu-screen-options .customize-help-toggle:hover,
.wp-core-ui #customize-controls .customize-info.open.active-menu-screen-options .customize-help-toggle:active,
.wp-core-ui #customize-controls .customize-info.open.active-menu-screen-options .customize-help-toggle:focus {
  color: #0073aa;
}
.wp-core-ui .customize-screen-options-toggle:focus:before,
.wp-core-ui #customize-controls .customize-info .customize-help-toggle:focus:before, .wp-core-ui.wp-customizer button:focus .toggle-indicator:before,
.wp-core-ui .menu-item-bar .item-delete:focus:before,
.wp-core-ui #available-menu-items .item-add:focus:before,
.wp-core-ui #customize-save-button-wrapper .save:focus,
.wp-core-ui #publish-settings:focus {
  box-shadow: 0 0 0 1px rgb(228.5391304348, 157.7173913043, 102.4608695652), 0 0 2px 1px #dd823b;
}
.wp-core-ui #customize-controls .customize-info.open .customize-help-toggle,
.wp-core-ui #customize-controls .customize-info .customize-help-toggle:focus,
.wp-core-ui #customize-controls .customize-info .customize-help-toggle:hover {
  color: #0073aa;
}
.wp-core-ui .control-panel-themes .customize-themes-section-title:focus,
.wp-core-ui .control-panel-themes .customize-themes-section-title:hover {
  border-right-color: #dd823b;
  color: #0073aa;
}
.wp-core-ui .control-panel-themes .theme-section .customize-themes-section-title.selected:after {
  background: #dd823b;
}
.wp-core-ui .control-panel-themes .customize-themes-section-title.selected {
  color: #0073aa;
}
.wp-core-ui #customize-theme-controls .control-section:hover > .accordion-section-title:after,
.wp-core-ui #customize-theme-controls .control-section .accordion-section-title:hover:after,
.wp-core-ui #customize-theme-controls .control-section.open .accordion-section-title:after,
.wp-core-ui #customize-theme-controls .control-section .accordion-section-title:focus:after,
.wp-core-ui #customize-outer-theme-controls .control-section:hover > .accordion-section-title:after,
.wp-core-ui #customize-outer-theme-controls .control-section .accordion-section-title:hover:after,
.wp-core-ui #customize-outer-theme-controls .control-section.open .accordion-section-title:after,
.wp-core-ui #customize-outer-theme-controls .control-section .accordion-section-title:focus:after {
  color: #0073aa;
}
.wp-core-ui .customize-control .attachment-media-view .button-add-media:focus {
  background-color: #fbfbfc;
  border-color: #dd823b;
  border-style: solid;
  box-shadow: 0 0 0 1px #dd823b;
  outline: 2px solid transparent;
}
.wp-core-ui .wp-full-overlay-footer .devices button:focus,
.wp-core-ui .wp-full-overlay-footer .devices button.active:hover {
  border-bottom-color: #dd823b;
}
.wp-core-ui .wp-full-overlay-footer .devices button:hover:before,
.wp-core-ui .wp-full-overlay-footer .devices button:focus:before {
  color: #dd823b;
}
.wp-core-ui .wp-full-overlay .collapse-sidebar:hover,
.wp-core-ui .wp-full-overlay .collapse-sidebar:focus {
  color: #dd823b;
}
.wp-core-ui .wp-full-overlay .collapse-sidebar:hover .collapse-sidebar-arrow,
.wp-core-ui .wp-full-overlay .collapse-sidebar:focus .collapse-sidebar-arrow {
  box-shadow: 0 0 0 1px rgb(228.5391304348, 157.7173913043, 102.4608695652), 0 0 2px 1px #dd823b;
}
.wp-core-ui.wp-customizer .theme-overlay .theme-header .close:focus, .wp-core-ui.wp-customizer .theme-overlay .theme-header .close:hover, .wp-core-ui.wp-customizer .theme-overlay .theme-header .right:focus, .wp-core-ui.wp-customizer .theme-overlay .theme-header .right:hover, .wp-core-ui.wp-customizer .theme-overlay .theme-header .left:focus, .wp-core-ui.wp-customizer .theme-overlay .theme-header .left:hover {
  border-bottom-color: #dd823b;
  color: #0073aa;
}