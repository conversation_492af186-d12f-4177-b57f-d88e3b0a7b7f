                    
                    
┌──────────────────┐
│ 39 Code Findings │
└──────────────────┘
                                                                                             
  [36m[22m[24m  advanced-custom-fields-pro/includes/admin/post-types/admin-field-group.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities('' . '<p><strong>' . esc_html__( 'Move Complete.', 'acf' ) . '</strong></p>'
              . '<p>' . sprintf( /* translators: Confirmation message once a field has been moved                      
              to a different field group. */ acf_punctify( __( 'The %1$s field can now be found                        
              in the %2$s field group', 'acf' ) ), esc_html( $field['label'] ), $link                                  
              //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ) . '</p>' . '<a                         
              href="#" class="button button-primary acf-close-popup">' . esc_html__( 'Close                            
              Modal', 'acf' ) . '</a>');                                                                               
          564┆ echo '' .
          565┆    '<p><strong>' . esc_html__( 'Move Complete.', 'acf' ) . '</strong></p>' .
          566┆    '<p>' . sprintf(
          567┆            /* translators: Confirmation message once a field has been moved to a different
               field group. */                                                                           
          568┆            acf_punctify( __( 'The %1$s field can now be found in the %2$s field group',
               'acf' ) ),                                                                             
          569┆            esc_html( $field['label'] ),
          570┆            $link  //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
          571┆    ) . '</p>' .
          572┆    '<a href="#" class="button button-primary acf-close-popup">' . esc_html__( 'Close
               Modal', 'acf' ) . '</a>';                                                           
   
   ❯❯❱ php.lang.security.tainted-user-input-in-php-script.tainted-user-input-in-php-script
          Untrusted input could be used to tamper with a web page rendering, which can lead to a Cross-site  
          scripting (XSS) vulnerability. XSS vulnerabilities occur when untrusted input executes malicious   
          JavaScript code, leading to issues such as account compromise and sensitive information leakage. To
          prevent this vulnerability, validate the user input, perform contextual output encoding or sanitize
          the input. In PHP you can encode or sanitize user input with `htmlspecialchars` or use automatic   
          context-aware escaping with a template engine such as Latte.                                       
          Details: https://sg.run/PKkY                                                                       
                                                                                                             
          565┆ '<p><strong>' . esc_html__( 'Move Complete.', 'acf' ) . '</strong></p>' .
          566┆ '<p>' . sprintf(
          567┆    /* translators: Confirmation message once a field has been moved to a different field
               group. */                                                                               
          568┆    acf_punctify( __( 'The %1$s field can now be found in the %2$s field group', 'acf' ) ),
          569┆    esc_html( $field['label'] ),
          570┆    $link  //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
          571┆ ) . '</p>' .
          572┆ '<a href="#" class="button button-primary acf-close-popup">' . esc_html__( 'Close Modal',
               'acf' ) . '</a>';                                                                        
                                                                                             
  [36m[22m[24m  advanced-custom-fields-pro/pro/fields/class-acf-field-flexible-content.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities(acf_esc_html( $title ));
          1324┆ echo acf_esc_html( $title );
                                                                            
  [36m[22m[24m  duplicator/installer/dup-installer/classes/class.http.php[0m
   ❯❯❱ php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off
          SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= 0)
          Details: https://sg.run/PJqv                                                        
                                                                                              
           33┆ curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            ⋮┆----------------------------------------
           75┆ curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
                                                        
  [36m[22m[24m  duplicator/src/Libs/Snap/SnapUtil.php[0m
   ❯❯❱ php.lang.security.phpinfo-use.phpinfo-use
          The 'phpinfo' function may reveal sensitive information about your environment.
          Details: https://sg.run/W82E                                                   
                                                                                         
          962┆ return phpinfo($flags);
                                                                   
  [36m[22m[24m  duplicator/views/packages/details/controller.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities(absint($package_id));
           69┆ <a href="?page=duplicator&action=detail&tab=detail&id=<?php echo absint($package_id); ?>"
               class="nav-tab <?php echo ($current_tab == 'detail') ? 'nav-tab-active' : '' ?>">        
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ echo htmlentities(absint($package_id));
           72┆ <a href="?page=duplicator&action=detail&tab=transfer&id=<?php echo absint($package_id); ?>"
               class="nav-tab <?php echo ($current_tab == 'transfer') ? 'nav-tab-active' : '' ?>">        
                                                                 
  [36m[22m[24m  duplicator/views/tools/diagnostics/logging.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities(str_replace('\\/', '/', json_encode($logurl)));
          109┆ $.get(<?php echo str_replace('\\/', '/', json_encode($logurl)); ?>, function(data) {
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ echo htmlentities(basename($logurl));
          180┆ <i class='fas fa-file-contract fa-fw'></i> <b><?php echo basename($logurl); ?></b> &nbsp; |
               &nbsp;                                                                                     
                                                                 
  [36m[22m[24m  ithemes-security-pro/core/admin-pages/init.php[0m
   ❯❯❱ php.lang.security.tainted-path-traversal.tainted-path-traversal
          Detected user input going into a php include or require command, which can lead to path traversal   
          and sensitive data being exposed. These commands can also lead to code execution. Instead, allowlist
          files that the user can access or rigorously validate user input.                                   
          Details: https://sg.run/WGrg                                                                        
                                                                                                              
          127┆ require_once( $file );
                                                                       
  [36m[22m[24m  ithemes-security-pro/core/admin-pages/page-debug.php[0m
   ❯❯❱ php.lang.security.search-active-debug.search-active-debug
          Debug logging is explicitly enabled. This can potentially disclose sensitive information and should
          never be active on production systems.                                                             
          Details: https://sg.run/PKeW                                                                       
                                                                                                             
           46┆ ini_set( 'display_errors', 1 );
                                                                      
  [36m[22m[24m  ithemes-security-pro/core/admin-pages/page-logs.php[0m
   ❯❯❱ php.lang.security.search-active-debug.search-active-debug
          Debug logging is explicitly enabled. This can potentially disclose sensitive information and should
          never be active on production systems.                                                             
          Details: https://sg.run/PKeW                                                                       
                                                                                                             
          199┆ ini_set( 'display_errors', 1 );
   
   ❯❯❱ php.lang.security.tainted-user-input-in-php-script.tainted-user-input-in-php-script
          Untrusted input could be used to tamper with a web page rendering, which can lead to a Cross-site  
          scripting (XSS) vulnerability. XSS vulnerabilities occur when untrusted input executes malicious   
          JavaScript code, leading to issues such as account compromise and sensitive information leakage. To
          prevent this vulnerability, validate the user input, perform contextual output encoding or sanitize
          the input. In PHP you can encode or sanitize user input with `htmlspecialchars` or use automatic   
          context-aware escaping with a template engine such as Latte.                                       
          Details: https://sg.run/PKkY                                                                       
                                                                                                             
          334┆ $raw_details['content'] = '<pre>' . preg_replace( '/^    /m', '', substr(
               ITSEC_Lib::get_print_r( $entry ), 23 ) ) . '</pre>';                     
            ⋮┆----------------------------------------
          338┆ $details['raw-details']['content'] = '<p><a class="itsec-log-raw-details-toggle" href="#">'
               . $this->translations['show_raw_details'] . '</a></p><div class="itsec-log-raw-details">' .
               $details['raw-details']['content'] . '</div>';                                             
   
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities($message['message']);
          361┆ <p><?php echo $message['message']; ?></p>
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ echo htmlentities($row['header']);
          369┆ <th scope="row"><?php echo $row['header']; ?></th>
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ echo htmlentities($row['content']);
          370┆ <td><?php echo $row['content'] ?></td>
                                                                          
  [36m[22m[24m  ithemes-security-pro/core/admin-pages/page-settings.php[0m
   ❯❯❱ php.lang.security.search-active-debug.search-active-debug
          Debug logging is explicitly enabled. This can potentially disclose sensitive information and should
          never be active on production systems.                                                             
          Details: https://sg.run/PKeW                                                                       
                                                                                                             
          139┆ ini_set( 'display_errors', 1 );
                                                    
  [36m[22m[24m  ithemes-security-pro/core/lib.php[0m
   ❯❯❱ php.lang.security.taint-cookie-secure-false.taint-cookie-secure-false
          Secure cookie flag is explicitly disabled. This will cause cookies to be transmitted over           
          unencrypted HTTP connections which can allow theft of confidential user data such as session tokens.
          Details: https://sg.run/GJx2                                                                        
                                                                                                              
          1670┆ setcookie( $name, ' ', ITSEC_Core::get_current_time_gmt() - YEAR_IN_SECONDS, COOKIEPATH,
               COOKIE_DOMAIN, false, false );                                                           
   
   ❯❯❱ php.lang.security.taint-cookie-http-false.taint-cookie-http-false
          HttpOnly cookie flag is explicitly disabled. This will cause cookies to be transmitted over         
          unencrypted HTTP connections which can allow theft of confidential user data such as session tokens.
          Details: https://sg.run/5qrX                                                                        
                                                                                                              
          1670┆ setcookie( $name, ' ', ITSEC_Core::get_current_time_gmt() - YEAR_IN_SECONDS, COOKIEPATH,
               COOKIE_DOMAIN, false, false );                                                           
                                                                              
  [36m[22m[24m  ithemes-security-pro/core/lib/class-itsec-wp-list-table.php[0m
   ❯❯❱ php.lang.security.injection.tainted-sql-string.tainted-sql-string
          User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL
          strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL    
          strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate   
          data from the database. Instead, use prepared statements (`$mysqli->prepare("INSERT INTO test(id,   
          label) VALUES (?, ?)");`) or a safe library.                                                        
          Details: https://sg.run/lZYG                                                                        
                                                                                                              
          531┆ $months = $wpdb->get_results( $wpdb->prepare( "
          532┆    SELECT DISTINCT YEAR( post_date ) AS year, MONTH( post_date ) AS month
          533┆    FROM $wpdb->posts
          534┆    WHERE post_type = %s
          535┆    $extra_checks
          536┆    ORDER BY post_date DESC
          537┆ ", $post_type ) );
                                                         
  [36m[22m[24m  ithemes-security-pro/core/lib/form.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities(" $var=\"$val\"");
          147┆ echo " $var=\"$val\"";
                                                                                   
  [36m[22m[24m  ithemes-security-pro/core/modules/security-check-pro/utility.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities("<response>{$_POST['expect']}:" . ( empty( $remote_ip_index ) ? 'false' :
              'true' ) . ':' . ( $ssl_supported ? 'true' : 'false' ) . '</response>');                              
          125┆ echo "<response>{$_POST['expect']}:" . ( empty( $remote_ip_index ) ? 'false' : 'true' ) .
               ':' . ( $ssl_supported ? 'true' : 'false' ) . '</response>';                             
                                                                                                   
  [36m[22m[24m  ithemes-security-pro/pro/user-security-check/class-itsec-user-security-check.php[0m
   ❯❯❱ php.lang.security.injection.tainted-sql-string.tainted-sql-string
          User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL
          strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL    
          strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate   
          data from the database. Instead, use prepared statements (`$mysqli->prepare("INSERT INTO test(id,   
          label) VALUES (?, ?)");`) or a safe library.                                                        
          Details: https://sg.run/lZYG                                                                        
                                                                                                              
          267┆ } elseif ( ! wp_verify_nonce( $_POST['nonce'], 'update-user_' . $user->ID ) ) {
                                                       
  [36m[22m[24m  polylang/include/widget-calendar.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities($output);
          140┆ echo $output;
   
   ❯❯❱ php.lang.security.injection.tainted-sql-string.tainted-sql-string
          User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL
          strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL    
          strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate   
          data from the database. Instead, use prepared statements (`$mysqli->prepare("INSERT INTO test(id,   
          label) VALUES (?, ?)");`) or a safe library.                                                        
          Details: https://sg.run/lZYG                                                                        
                                                                                                              
          176┆ $thismonth = $wpdb->get_var( "SELECT DATE_FORMAT((DATE_ADD('{$thisyear}0101', INTERVAL $d
               DAY) ), '%m')" );                                                                        
            ⋮┆----------------------------------------
          197┆ "SELECT MONTH(post_date) AS month, YEAR(post_date) AS year
          198┆ FROM $wpdb->posts $join_clause
          199┆ WHERE post_date < '$thisyear-$thismonth-01'
          200┆ AND post_type = %s AND post_status = 'publish' $where_clause
          201┆ ORDER BY post_date DESC
          202┆ LIMIT 1",
            ⋮┆----------------------------------------
          208┆ "SELECT MONTH(post_date) AS month, YEAR(post_date) AS year
          209┆ FROM $wpdb->posts $join_clause
          210┆ WHERE post_date > '$thisyear-$thismonth-{$last_day} 23:59:59'
          211┆ AND post_type = %s AND post_status = 'publish' $where_clause
          212┆ ORDER BY post_date ASC
          213┆ LIMIT 1",
            ⋮┆----------------------------------------
          251┆ "SELECT DISTINCT DAYOFMONTH(post_date)
          252┆ FROM $wpdb->posts $join_clause WHERE post_date >= '{$thisyear}-{$thismonth}-01 00:00:00'
          253┆ AND post_type = %s AND post_status = 'publish'
          254┆ AND post_date <= '{$thisyear}-{$thismonth}-{$last_day} 23:59:59' $where_clause",
   
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities($calendar_output);
          349┆ echo $calendar_output;
                                                       
  [36m[22m[24m  wp-media-folder/class/class-main.php[0m
   ❯❯❱ php.lang.security.tainted-url-to-connection.tainted-url-to-connection
          Untrusted input might be used to build an HTTP request, which can lead to a Server-side request    
          forgery (SSRF) vulnerability. SSRF allows an attacker to send crafted requests from the server side
          to other internal or external systems. SSRF can lead to unauthorized access to sensitive data and, 
          in some cases, allow the attacker to control applications or systems that trust the vulnerable     
          service. To prevent this vulnerability, avoid allowing user input to craft the base request.       
          Instead, treat it as part of the path or query parameter and encode it appropriately. When user    
          input is necessary to prepare the HTTP request, perform strict input validation. Additionally,     
          whenever possible, use allowlists to only interact with expected, trusted domains.                 
          Details: https://sg.run/0Bb5                                                                       
                                                                                                             
          3796┆ $upload        = file_put_contents($upload_folder . '/' . $fname, $content);
            ⋮┆----------------------------------------
          3800┆ $upload        = file_put_contents($upload_folder . '/' . $fname, $content);
                                                                   
  [36m[22m[24m  wp-media-folder/class/class-wp-folder-option.php[0m
   ❯❯❱ php.lang.security.injection.tainted-sql-string.tainted-sql-string
          User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL
          strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL    
          strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate   
          data from the database. Instead, use prepared statements (`$mysqli->prepare("INSERT INTO test(id,   
          label) VALUES (?, ?)");`) or a safe library.                                                        
          Details: https://sg.run/lZYG                                                                        
                                                                                                              
          3200┆ $eml_categories = $wpdb->get_results($wpdb->prepare('SELECT * FROM ' . $wpdb->terms . ' as
               t INNER JOIN ' . $wpdb->term_taxonomy . ' AS tt ON tt.term_id = t.term_id WHERE taxonomy = 
               "media_category" AND t.term_id IN ('. $ids .') LIMIT %d OFFSET %d', array((int) $limit,    
               (int) $offset)));                                                                          
                                                                             
  [36m[22m[24m  wp-media-folder/class/pages/settings/image_compression.php[0m
   ❯❯❱ php.lang.security.injection.tainted-sql-string.tainted-sql-string
          User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL
          strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL    
          strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate   
          data from the database. Instead, use prepared statements (`$mysqli->prepare("INSERT INTO test(id,   
          label) VALUES (?, ?)");`) or a safe library.                                                        
          Details: https://sg.run/lZYG                                                                        
                                                                                                              
           80┆ $url          = 'update.php?action=install-plugin&plugin=' . urlencode($plugin);
                                                              
  [36m[22m[24m  wp-media-folder/class/wpmf-fillter-size.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities(@$_GET['attachment_size']);
           67┆ size = '<?php echo @$_GET['attachment_size'] ?>';
            ⋮┆----------------------------------------
           ▶▶┆ Autofix ▶ echo htmlentities(@$_GET['attachment_weight']);
           69┆ weight = '<?php echo @$_GET['attachment_weight'] ?>';
                                                                  
  [36m[22m[24m  wp-media-folder/jutranslation/jutranslation.php[0m
   ❯❯❱ php.lang.security.injection.echoed-request.echoed-request
          `Echo`ing user input risks cross-site scripting vulnerability. You should use `htmlentities()` when
          showing data to users.                                                                             
          Details: https://sg.run/Bqqb                                                                       
                                                                                                             
           ▶▶┆ Autofix ▶ echo htmlentities(json_encode( array('status' => 'success', 'datas' => array('language' =>
              $language, 'strings' => $final_result)) ));                                                          
          622┆ echo json_encode(
          623┆     array('status' => 'success', 'datas' => array('language' => $language, 'strings' =>
               $final_result))                                                                        
          624┆ );
                                                          
  [36m[22m[24m  wp-media-folder/juupdater/juupdater.php[0m
   ❯❯❱ php.lang.security.injection.tainted-sql-string.tainted-sql-string
          User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL
          strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL    
          strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate   
          data from the database. Instead, use prepared statements (`$mysqli->prepare("INSERT INTO test(id,   
          label) VALUES (?, ?)");`) or a safe library.                                                        
          Details: https://sg.run/lZYG                                                                        
                                                                                                              
          996┆ $message = sprintf('Unknown update checker status "%s"', htmlentities($status));

